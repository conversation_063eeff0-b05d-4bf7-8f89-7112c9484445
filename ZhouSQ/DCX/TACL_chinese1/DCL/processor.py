# -*- coding:utf-8 -*-

import torch
import os
from openprompt.data_utils.utils import Input<PERSON><PERSON>mple
from tqdm import tqdm

base_path = "./"


class WOSProcessor:

    def __init__(self, ratio=-1, seed=171, shot=-1, ratio_flag=0):
        super().__init__()
        from dataset.WebOfScience.trans_format import get_mapping1
        from dataset.WebOfScience.my_dataset import sub_dataset
        self.name = 'WebOfScience'
        # (
        #     label0_list, label1_list, label2_list, label3_list, label4_list, label5_list, label6_list,
        #     label7_list, label8_list,
        #     label0_label2id, label1_label2id,
        #     label0_to_label1_mapping, label1_to_label0_mapping,
        #     label1_to_label2_mapping, label2_to_label1_mapping,
        #     label2_to_label3_mapping, label3_to_label2_mapping,
        #     label3_to_label4_mapping, label4_to_label3_mapping,
        #     label4_to_label5_mapping, label5_to_label4_mapping,
        #     label5_to_label6_mapping, label6_to_label5_mapping,
        #     label6_to_label7_mapping, label7_to_label6_mapping,
        #     label7_to_label8_mapping, label8_to_label7_mapping,
        # ) = get_mapping1()
        (
            label0_list, label1_list, label2_list, label3_list, label4_list, label5_list, label6_list,
            label0_label2id, label1_label2id,
            label0_to_label1_mapping, label1_to_label0_mapping,
            label1_to_label2_mapping, label2_to_label1_mapping,
            label2_to_label3_mapping, label3_to_label2_mapping,
            label3_to_label4_mapping, label4_to_label3_mapping,
            label4_to_label5_mapping, label5_to_label4_mapping,
            label5_to_label6_mapping, label6_to_label5_mapping,
        ) = get_mapping1()

        # self.labels = label8_list  # 修改为使用最细粒度的label8
        self.labels = label6_list  # 修改为使用最细粒度的label6
        self.coarse_labels = label0_list
        # self.all_labels = label0_list + label1_list + label2_list + label3_list + label4_list + label5_list + label6_list + label7_list + label8_list
        self.all_labels = label0_list + label1_list + label2_list + label3_list + label4_list + label5_list + label6_list
        # self.label_list = [label0_list, label1_list, label2_list, label3_list, label4_list, label5_list, label6_list, label7_list, label8_list]
        self.label_list = [label0_list, label1_list, label2_list, label3_list, label4_list, label5_list, label6_list]
        
        # 添加所有层级的映射关系
        self.label0_to_label1_mapping = label0_to_label1_mapping
        self.label1_to_label0_mapping = label1_to_label0_mapping
        self.label1_to_label2_mapping = label1_to_label2_mapping
        self.label2_to_label1_mapping = label2_to_label1_mapping
        self.label2_to_label3_mapping = label2_to_label3_mapping
        self.label3_to_label2_mapping = label3_to_label2_mapping
        self.label3_to_label4_mapping = label3_to_label4_mapping
        self.label4_to_label3_mapping = label4_to_label3_mapping
        self.label4_to_label5_mapping = label4_to_label5_mapping
        self.label5_to_label4_mapping = label5_to_label4_mapping
        self.label5_to_label6_mapping = label5_to_label6_mapping
        self.label6_to_label5_mapping = label6_to_label5_mapping
        # self.label6_to_label7_mapping = label6_to_label7_mapping
        # self.label7_to_label6_mapping = label7_to_label6_mapping
        # self.label7_to_label8_mapping = label7_to_label8_mapping
        # self.label8_to_label7_mapping = label8_to_label7_mapping


        self.data_path = '/home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/dataset/WebOfScience'
        self.flat_slot2value, self.value2slot, self.depth2label = self.get_tree_info()
        # 更新hier_mapping包含所有层级
        # self.hier_mapping = [
        #     [label0_to_label1_mapping, label1_to_label0_mapping],
        #     [label1_to_label2_mapping, label2_to_label1_mapping],
        #     [label2_to_label3_mapping, label3_to_label2_mapping],
        #     [label3_to_label4_mapping, label4_to_label3_mapping],
        #     [label4_to_label5_mapping, label5_to_label4_mapping],
        #     [label5_to_label6_mapping, label6_to_label5_mapping],
        #     [label6_to_label7_mapping, label7_to_label6_mapping],
        #     [label7_to_label8_mapping, label8_to_label7_mapping]
        # ]
        self.hier_mapping = [
            [label0_to_label1_mapping, label1_to_label0_mapping],
            [label1_to_label2_mapping, label2_to_label1_mapping],
            [label2_to_label3_mapping, label3_to_label2_mapping],
            [label3_to_label4_mapping, label4_to_label3_mapping],
            [label4_to_label5_mapping, label5_to_label4_mapping],
            [label5_to_label6_mapping, label6_to_label5_mapping],
        ]


        self.ratio = ratio
        self.seed = seed
        self.shot = shot
        self.dataset = sub_dataset(self.shot, self.seed, self.ratio, ratio_flag=ratio_flag)
        print("length dataset['train']:", len(self.dataset['train']))

        self.train_data = self.get_dataset("train")

        self.dev_data = self.get_dataset("val")
        self.test_data = self.get_dataset("test")

        self.train_example = self.convert_data_to_examples(self.train_data)
        self.dev_example = self.convert_data_to_examples(self.dev_data)
        self.test_example = self.convert_data_to_examples(self.test_data)

        self.train_inputs = [i[0] for i in self.train_data]
        self.dev_inputs = [i[0] for i in self.dev_data]
        self.test_inputs = [i[0] for i in self.test_data]

        self.size = len(self.train_example) + len(self.test_example)

    def get_tree_info(self):
        # flat_slot2value = torch.load(os.path.join(self.data_path, 'slot.pt'))
        flat_slot2value = torch.load(os.path.join(self.data_path, 'slot.pt'), weights_only=False)

        value2slot = {}
        num_class = 0
        for s in flat_slot2value:
            for v in flat_slot2value[s]:
                value2slot[v] = s
                if num_class < v:
                    num_class = v
        num_class += 1
        for i in range(num_class):
            if i not in value2slot:
                value2slot[i] = -1

        def get_depth(x):
            depth = 0
            while value2slot[x] != -1:
                depth += 1
                x = value2slot[x]
            return depth

        depth_dict = {i: get_depth(i) for i in range(num_class)}
        max_depth = depth_dict[max(depth_dict, key=depth_dict.get)] + 1
        depth2label = {i: [a for a in depth_dict if depth_dict[a] == i] for i in range(max_depth)}
        return flat_slot2value, value2slot, depth2label

    def get_dataset(self, type="train"):
        data = []
        cur_dataset = self.dataset[type]
        length = len(cur_dataset)
        for i in tqdm(range(length)):
            text_a = cur_dataset[i][0]
            label = cur_dataset[i][1]
            data.append([text_a, label])
        return data

    def convert_data_to_examples(self, data):
        examples = []
        for idx, sub_data in enumerate(data):
            examples.append(InputExample(guid=str(idx), text_a=sub_data[0], label=sub_data[1]))

        return examples

class DBPProcessor:

    def __init__(self, ratio=-1, seed=171, shot=-1, ratio_flag=0):
        super().__init__()
        self.name = 'WebOfScience'
        from dataset.DBpedia.trans_format import get_mapping
        from dataset.DBpedia.my_dataset import sub_dataset
        label0_list, label1_list, label2_list, label0_label2id, label1_label2id, label2_label2id, label0_to_label1_mapping, label1_to_label2_mapping, label1_to_label0_mapping, label2_to_label1_mapping = get_mapping()
        self.labels = label2_list
        self.coarse_labels = label0_list
        self.all_labels = label0_list + label1_list + label2_list
        self.label_list = [label0_list, label1_list, label2_list]
        self.label0_to_label1_mapping = label0_to_label1_mapping
        self.label1_to_label2_mapping = label1_to_label2_mapping
        self.label1_to_label0_mapping = label1_to_label0_mapping
        self.label2_to_label1_mapping = label2_to_label1_mapping

        self.data_path = os.path.join(base_path, "dataset", "DBpedia")
        self.flat_slot2value, self.value2slot, self.depth2label = self.get_tree_info()
        self.hier_mapping = [[label0_to_label1_mapping, label1_to_label0_mapping], [label1_to_label2_mapping, label2_to_label1_mapping]]

        self.ratio = ratio
        self.seed = seed
        self.shot = shot
        self.dataset = sub_dataset(self.shot, self.seed, self.ratio, ratio_flag=ratio_flag)
        print("length dataset['train']:", len(self.dataset['train']))

        self.train_data = self.get_dataset("train")

        self.dev_data = self.get_dataset("val")
        self.test_data = self.get_dataset("test")
        self.train_example = self.convert_data_to_examples(self.train_data)
        self.dev_example = self.convert_data_to_examples(self.dev_data)
        self.test_example = self.convert_data_to_examples(self.test_data)

        self.train_inputs = [i[0] for i in self.train_data]
        self.dev_inputs = [i[0] for i in self.dev_data]
        self.test_inputs = [i[0] for i in self.test_data]

        self.size = len(self.train_example) + len(self.test_example)

    def get_tree_info(self):
        flat_slot2value = torch.load(os.path.join(self.data_path, 'slot.pt'))

        value2slot = {}
        num_class = 0
        for s in flat_slot2value:
            for v in flat_slot2value[s]:
                value2slot[v] = s
                if num_class < v:
                    num_class = v
        num_class += 1
        for i in range(num_class):
            if i not in value2slot:
                value2slot[i] = -1

        def get_depth(x):
            depth = 0
            while value2slot[x] != -1:
                depth += 1
                x = value2slot[x]
            return depth

        depth_dict = {i: get_depth(i) for i in range(num_class)}
        max_depth = depth_dict[max(depth_dict, key=depth_dict.get)] + 1
        depth2label = {i: [a for a in depth_dict if depth_dict[a] == i] for i in range(max_depth)}
        return flat_slot2value, value2slot, depth2label

    def get_dataset(self, type="train"):
        data = []
        cur_dataset = self.dataset[type]
        length = len(cur_dataset)
        for i in tqdm(range(length)):
            text_a = cur_dataset[i][0]
            label = cur_dataset[i][1]
            data.append([text_a, label])
        return data

    def convert_data_to_examples(self, data):
        examples = []
        for idx, sub_data in enumerate(data):
            examples.append(InputExample(guid=str(idx), text_a=sub_data[0], label=sub_data[1]))

        return examples

PROCESSOR = {
    "wos": WOSProcessor,
    "WebOfScience": WOSProcessor,
    "dbp": DBPProcessor,
    "DBpedia": DBPProcessor,
}

