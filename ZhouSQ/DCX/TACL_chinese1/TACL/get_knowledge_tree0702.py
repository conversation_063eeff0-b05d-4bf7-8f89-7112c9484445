import pandas as pd
from collections import defaultdict
import json
import openpyxl
import csv

class KnowledgeNode:
    """知识点树节点类"""
    def __init__(self, id, name, tree_code, tree_level):
        self.id = id                  # 知识点ID
        self.name = name              # 知识点名称
        self.tree_code = tree_code    # 树编码
        self.tree_level = tree_level  # 树的层级
        self.children = []            # 子节点列表
    
    def add_child(self, child):
        """添加子节点"""
        self.children.append(child)
    
    def __repr__(self):
        return f"KnowledgeNode(id={self.id}, name={self.name}, level={self.tree_level}, children={len(self.children)})"

def build_knowledge_tree_from_csv(csv_path, output_excel_path):
    """
    从CSV文件构建知识树并导出到Excel
    
    参数:
        csv_path: 输入CSV文件路径
        output_excel_path: 输出Excel文件路径
    """
    # 读取CSV文件
    df = pd.read_csv(csv_path)
    
    # 创建所有节点字典
    nodes_dict = {}
    # 按tree_level分组的节点
    level_nodes = defaultdict(list)
    
    # 创建所有节点
    for _, row in df.iterrows():
        node = KnowledgeNode(
            id=row['id'],
            name=row['name'],
            tree_code=row['tree_code'],
            tree_level=row['tree_level']
        )
        nodes_dict[row['id']] = node
        level_nodes[row['tree_level']].append(node)
    
    # 构建树结构
    root_nodes = []
    for level in sorted(level_nodes.keys()):
        if level == 1:
            root_nodes = level_nodes[level]
            continue
            
        for node in level_nodes[level]:
            # 从tree_code中提取父节点ID
            parent_code = node.tree_code.split('.')[-3]  # 假设父节点编码是倒数第3部分
            parent_id = int(parent_code)
            
            # 添加父子关系
            if parent_id in nodes_dict:
                nodes_dict[parent_id].add_child(node)
    
    # 准备导出数据
    export_data = []
    for node in nodes_dict.values():
        # 确定父节点ID
        parent_id = None
        if node.tree_level > 1:
            parent_code = node.tree_code.split('.')[-3]
            parent_id = int(parent_code)
        
        # 确定节点类型
        node_type = f"{node.tree_level-1}级知识点"
        if node.tree_level == 1:
            node_type = "0级知识点"
        
        export_data.append({
            'ID': node.id,
            '名称': node.name,
            '父级ID': parent_id,
            '节点类型': node_type
        })
    
    # 导出到Excel
    export_df = pd.DataFrame(export_data)
    export_df.to_excel(output_excel_path, index=False)
    print(f"知识树已成功导出到: {output_excel_path}")


def build_knowledge_tree_from_json(json_path, output_excel_path):
    """
    从JSON文件构建知识树并导出到Excel
    
    参数:
        json_path: 输入JSON文件路径
        output_excel_path: 输出Excel文件路径
    """
    # 读取JSON文件
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 创建所有节点字典
    nodes_dict = {}
    # 按tree_level分组的节点
    level_nodes = defaultdict(list)
    
    # 创建所有节点
    for item in data:
        node = KnowledgeNode(
            id=item['id'],
            name=item['name'],
            tree_code=item['tree_code'],
            tree_level=item['tree_level']
        )
        nodes_dict[item['id']] = node
        level_nodes[item['tree_level']].append(node)
    
    # 构建树结构
    root_nodes = []
    for level in sorted(level_nodes.keys()):
        if level == 1:
            root_nodes = level_nodes[level]
            continue
            
        for node in level_nodes[level]:
            # 从tree_code中提取父节点ID
            parent_code = node.tree_code.split('.')[-3]  # 假设父节点编码是倒数第3部分
            parent_id = int(parent_code)
            
            # 添加父子关系
            if parent_id in nodes_dict:
                nodes_dict[parent_id].add_child(node)
    
    # 准备导出数据
    export_data = []
    for node in nodes_dict.values():
        # 确定父节点ID
        parent_id = None
        if node.tree_level > 1:
            parent_code = node.tree_code.split('.')[-3]
            parent_id = int(parent_code)
        
        # 确定节点类型
        node_type = f"{node.tree_level-1}级知识点"
        if node.tree_level == 1:
            node_type = "0级知识点"
        
        export_data.append({
            'ID': node.id,
            '名称': node.name,
            '父级ID': parent_id,
            '节点类型': node_type
        })
    
    # 导出到Excel
    export_df = pd.DataFrame(export_data)
    export_df.to_excel(output_excel_path, index=False)
    print(f"知识树已成功导出到: {output_excel_path}")



def load_data_from_csv(file_path):
    """读取 CSV 文件，返回数据列表"""
    data = []
    with open(file_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            # 转换 id 为 int（如果需要）
            row['id'] = int(row['id'])
            data.append(row)
    return data

def build_parent_map(data):
    """根据 tree_code 构建 {id: parent_id} 映射"""
    parent_map = {}
    code_map = {}

    for item in data:
        normalized_code = item['tree_code'].strip('.')
        code_map[normalized_code] = item['id']

    for item in data:
        node_id = item['id']
        normalized_code = item['tree_code'].strip('.')

        parts = normalized_code.split('.')
        if len(parts) > 1:
            parent_code = '.'.join(parts[:-1])
            parent_id = code_map.get(parent_code)
        else:
            parent_id = None

        parent_map[node_id] = parent_id

    return parent_map

def build_tree(data, parent_map):
    """根据 parent_map 构建树结构"""
    tree = []
    id_node_map = {}

    for item in data:
        node = {
            'id': item['id'],
            'name': item['name'],
            'children': []
        }
        id_node_map[item['id']] = node

    for item in data:
        node_id = item['id']
        parent_id = parent_map[node_id]

        if parent_id is None:
            tree.append(id_node_map[node_id])
        else:
            parent_node = id_node_map[parent_id]
            parent_node['children'].append(id_node_map[node_id])

    return tree

def flatten_tree(tree, parent_id=None, level=0, result=None):
    """递归扁平化树结构"""
    if result is None:
        result = []

    for node in tree:
        node_type = f"{level}级知识点"
        result.append({
            'ID': node['id'],
            '名称': node['name'],
            '父级ID': parent_id,
            '节点类型': node_type
        })

        # 递归处理子节点
        if node.get('children'):
            flatten_tree(node['children'], parent_id=node['id'], level=level + 1, result=result)

    return result

def save_to_excel(data, file_path):
    """将扁平数据保存为 Excel"""
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "知识树"

    # 写入标题
    ws.append(['ID', '名称', '父级ID', '节点类型'])

    # 写入数据
    for item in data:
        ws.append([item['ID'], item['名称'], item['父级ID'], item['节点类型']])

    wb.save(file_path)
    print(f"已保存为 Excel 文件：{file_path}")

def main1():
    # 读取 JSON 文件
    with open('d:/TACL_data_process/math2/math2_knowledge_tree.json', 'r', encoding='utf-8') as f:
        tree = json.load(f)

    # 扁平化树结构
    flat_data = flatten_tree(tree)

    # 保存为 Excel
    save_to_excel(flat_data, 'd:/TACL_data_process/math2/math2_knowledge_tree.xlsx')


def main():
    file_path = 'd:/TACL_data_process/math2/math2_knowledge_tree.csv'  # 你的 CSV 文件路径
    data = load_data_from_csv(file_path)
    parent_map = build_parent_map(data)
    tree = build_tree(data, parent_map)

    # 输出为 JSON（缩进方便查看）
    with open('d:/TACL_data_process/math2/math2_knowledge_tree.json', 'w', encoding='utf-8') as f:
        json.dump(tree, f, indent=2, ensure_ascii=False)

    print("知识树构建完成，输出文件：knowledge_tree.json")

if __name__ == '__main__':
    # build_knowledge_tree_from_json(
    #     json_path="d:/TACL_data_process/xiaoshu/math1_knowledge_tree_0703.json",
    #     output_excel_path="d:/TACL_data_process/xiaoshu/math1_knowledge_tree_0703.xlsx"
    # )
    # main()
    main1()
