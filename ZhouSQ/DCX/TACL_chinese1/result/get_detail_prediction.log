--- Log Start: 2025-08-12 13:59:08 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 897 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 5924 ------------
length dataset['train']: 5924
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([6, 768])
depth 2: torch.Size([21, 768])
depth 3: torch.Size([169, 768])
depth 4: torch.Size([523, 768])
depth 5: torch.Size([850, 768])
depth 6: torch.Size([870, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持7层分层分类
📊 各层标签数量: [1, 6, 21, 169, 523, 850, 870]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 897 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 423
   - 总知识点关联数: 897
🚀 开始批量预测...
🔄 开始批量预测 423 条文本（批大小: 32）...
/home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/util/data_loader.py:131: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
   已分析 50/423 条题目...
   已分析 100/423 条题目...
   已分析 150/423 条题目...
   已分析 200/423 条题目...
   已分析 250/423 条题目...
   已分析 300/423 条题目...
   已分析 350/423 条题目...
   已分析 400/423 条题目...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 423
   - 成功预测数量: 423
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 65.16%
   - 完全匹配题目: 185
   - 部分匹配题目: 229
   - 无匹配题目: 9

📚 知识点数量分析:
   - 平均知识点数量: 2.12
   - 最多知识点数量: 10
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 188 题）：
   - 命中 1 个知识点: 185题，占比 98.40%
   - 未命中任何知识点: 3题，占比 1.60%

🧩 题目含 2 个知识点（共 129 题）：
   - 命中 1 个知识点: 125题，占比 96.90%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 未命中任何知识点: 4题，占比 3.10%

🧩 题目含 3 个知识点（共 59 题）：
   - 命中 1 个知识点: 59题，占比 100.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 0题，占比 0.00%

🧩 题目含 4 个知识点（共 13 题）：
   - 命中 1 个知识点: 13题，占比 100.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 0题，占比 0.00%
   - 命中 4 个知识点: 0题，占比 0.00%

🧩 题目含 5 个知识点（共 12 题）：
   - 命中 1 个知识点: 11题，占比 91.67%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 0题，占比 0.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 未命中任何知识点: 1题，占比 8.33%

🧩 题目含 6 个知识点（共 9 题）：
   - 命中 1 个知识点: 9题，占比 100.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 0题，占比 0.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%

🧩 题目含 7 个知识点（共 5 题）：
   - 命中 1 个知识点: 5题，占比 100.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 0题，占比 0.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%

🧩 题目含 8 个知识点（共 2 题）：
   - 命中 1 个知识点: 2题，占比 100.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 0题，占比 0.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%
   - 命中 8 个知识点: 0题，占比 0.00%

🧩 题目含 9 个知识点（共 3 题）：
   - 命中 1 个知识点: 3题，占比 100.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 0题，占比 0.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%
   - 命中 8 个知识点: 0题，占比 0.00%
   - 命中 9 个知识点: 0题，占比 0.00%

🧩 题目含 10 个知识点（共 3 题）：
   - 命中 1 个知识点: 2题，占比 66.67%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 0题，占比 0.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%
   - 命中 8 个知识点: 0题，占比 0.00%
   - 命中 9 个知识点: 0题，占比 0.00%
   - 命中 10 个知识点: 0题，占比 0.00%
   - 未命中任何知识点: 1题，占比 33.33%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/multi_knowledge_prediction_results_old_test.json

🏁 测试完成！
--- Log Start: 2025-08-12 13:59:51 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 897 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 5924 ------------
length dataset['train']: 5924
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([6, 768])
depth 2: torch.Size([21, 768])
depth 3: torch.Size([169, 768])
depth 4: torch.Size([523, 768])
depth 5: torch.Size([850, 768])
depth 6: torch.Size([870, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持7层分层分类
📊 各层标签数量: [1, 6, 21, 169, 523, 850, 870]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 897 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 423
   - 总知识点关联数: 897
🚀 开始批量预测...
🔄 开始批量预测 423 条文本（批大小: 32）...
/home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/util/data_loader.py:131: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
   已分析 50/423 条题目...
   已分析 100/423 条题目...
   已分析 150/423 条题目...
   已分析 200/423 条题目...
   已分析 250/423 条题目...
   已分析 300/423 条题目...
   已分析 350/423 条题目...
   已分析 400/423 条题目...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 423
   - 成功预测数量: 423
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 87.22%
   - 完全匹配题目: 311
   - 部分匹配题目: 110
   - 无匹配题目: 2

📚 知识点数量分析:
   - 平均知识点数量: 2.12
   - 最多知识点数量: 10
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 188 题）：
   - 命中 1 个知识点: 188题，占比 100.00%

🧩 题目含 2 个知识点（共 129 题）：
   - 命中 1 个知识点: 4题，占比 3.10%
   - 命中 2 个知识点: 123题，占比 95.35%
   - 未命中任何知识点: 2题，占比 1.55%

🧩 题目含 3 个知识点（共 59 题）：
   - 命中 1 个知识点: 1题，占比 1.69%
   - 命中 2 个知识点: 58题，占比 98.31%
   - 命中 3 个知识点: 0题，占比 0.00%

🧩 题目含 4 个知识点（共 13 题）：
   - 命中 1 个知识点: 1题，占比 7.69%
   - 命中 2 个知识点: 12题，占比 92.31%
   - 命中 3 个知识点: 0题，占比 0.00%
   - 命中 4 个知识点: 0题，占比 0.00%

🧩 题目含 5 个知识点（共 12 题）：
   - 命中 1 个知识点: 1题，占比 8.33%
   - 命中 2 个知识点: 11题，占比 91.67%
   - 命中 3 个知识点: 0题，占比 0.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%

🧩 题目含 6 个知识点（共 9 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 9题，占比 100.00%
   - 命中 3 个知识点: 0题，占比 0.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%

🧩 题目含 7 个知识点（共 5 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 5题，占比 100.00%
   - 命中 3 个知识点: 0题，占比 0.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%

🧩 题目含 8 个知识点（共 2 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 2题，占比 100.00%
   - 命中 3 个知识点: 0题，占比 0.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%
   - 命中 8 个知识点: 0题，占比 0.00%

🧩 题目含 9 个知识点（共 3 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 3题，占比 100.00%
   - 命中 3 个知识点: 0题，占比 0.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%
   - 命中 8 个知识点: 0题，占比 0.00%
   - 命中 9 个知识点: 0题，占比 0.00%

🧩 题目含 10 个知识点（共 3 题）：
   - 命中 1 个知识点: 1题，占比 33.33%
   - 命中 2 个知识点: 2题，占比 66.67%
   - 命中 3 个知识点: 0题，占比 0.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%
   - 命中 8 个知识点: 0题，占比 0.00%
   - 命中 9 个知识点: 0题，占比 0.00%
   - 命中 10 个知识点: 0题，占比 0.00%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/multi_knowledge_prediction_results_old_test-1.json

🏁 测试完成！
--- Log Start: 2025-08-12 14:00:43 ---
🎯 多知识点题目预测测试工具
================================================================================
📁 加载数据文件: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/dataset/WebOfScience/wos_test.json
✅ 成功加载 897 条数据

🚀 初始化分类器...
🔧 设置随机种子...
🔧 加载processor...
------------ using seed 171 ------------
------------ loading few-shot for 30 shot ------------
load index from path /home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/dataset/WebOfScience/few-shot/seed_171-shot_30.json
------------ length few-shot: 5924 ------------
length dataset['train']: 5924
🔧 加载PLM和tokenizer...
🔧 加载模板...
✅ 找到模板文件: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/template/wos_mask_template.txt
✅ 模板加载完成
🔧 创建verbalizer列表...
🔧 构建prompt模型...
using label emb for soft verbalizer
depth 0: torch.Size([1, 768])
depth 1: torch.Size([6, 768])
depth 2: torch.Size([21, 768])
depth 3: torch.Size([169, 768])
depth 4: torch.Size([523, 768])
depth 5: torch.Size([850, 768])
depth 6: torch.Size([870, 768])
🔧 移动模型到GPU...
🔧 加载训练好的权重...
🔧 加载训练集嵌入...
✅ 模型初始化完成！支持7层分层分类
📊 各层标签数量: [1, 6, 21, 169, 523, 850, 870]
✅ 分类器初始化成功

🔍 批量测试多知识点题目预测效果（采样 897 条）...
📊 多知识点题目统计:
   - 多知识点题目数量: 423
   - 总知识点关联数: 897
🚀 开始批量预测...
🔄 开始批量预测 423 条文本（批大小: 32）...
/home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/util/data_loader.py:131: UserWarning: Creating a tensor from a list of numpy.ndarrays is extremely slow. Please consider converting the list to a single numpy.ndarray with numpy.array() before converting to a tensor. (Triggered internally at /pytorch/torch/csrc/utils/tensor_new.cpp:254.)
  all_input_ids = torch.tensor([convert_tensor_to_numpy(f['input_ids']) for f in self.tensor_dataset],
✅ 批量预测完成！
✅ 批量预测完成！
🔍 分析预测结果...
   已分析 50/423 条题目...
   已分析 100/423 条题目...
   已分析 150/423 条题目...
   已分析 200/423 条题目...
   已分析 250/423 条题目...
   已分析 300/423 条题目...
   已分析 350/423 条题目...
   已分析 400/423 条题目...
✅ 结果分析完成！

📊 预测结果分析:
============================================================
📈 总体统计:
   - 测试题目数量: 423
   - 成功预测数量: 423
   - 预测失败数量: 0

🎯 匹配效果:
   - 平均匹配率: 94.12%
   - 完全匹配题目: 370
   - 部分匹配题目: 52
   - 无匹配题目: 1

📚 知识点数量分析:
   - 平均知识点数量: 2.12
   - 最多知识点数量: 10
   - 最少知识点数量: 1

📊 按知识点数量的匹配率（含细粒度TopK命中分析）:

🧩 题目含 1 个知识点（共 188 题）：
   - 命中 1 个知识点: 188题，占比 100.00%

🧩 题目含 2 个知识点（共 129 题）：
   - 命中 1 个知识点: 4题，占比 3.10%
   - 命中 2 个知识点: 124题，占比 96.12%
   - 未命中任何知识点: 1题，占比 0.78%

🧩 题目含 3 个知识点（共 59 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 1题，占比 1.69%
   - 命中 3 个知识点: 58题，占比 98.31%

🧩 题目含 4 个知识点（共 13 题）：
   - 命中 1 个知识点: 1题，占比 7.69%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 12题，占比 92.31%
   - 命中 4 个知识点: 0题，占比 0.00%

🧩 题目含 5 个知识点（共 12 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 1题，占比 8.33%
   - 命中 3 个知识点: 11题，占比 91.67%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%

🧩 题目含 6 个知识点（共 9 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 9题，占比 100.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%

🧩 题目含 7 个知识点（共 5 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 5题，占比 100.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%

🧩 题目含 8 个知识点（共 2 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 2题，占比 100.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%
   - 命中 8 个知识点: 0题，占比 0.00%

🧩 题目含 9 个知识点（共 3 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 0题，占比 0.00%
   - 命中 3 个知识点: 3题，占比 100.00%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%
   - 命中 8 个知识点: 0题，占比 0.00%
   - 命中 9 个知识点: 0题，占比 0.00%

🧩 题目含 10 个知识点（共 3 题）：
   - 命中 1 个知识点: 0题，占比 0.00%
   - 命中 2 个知识点: 1题，占比 33.33%
   - 命中 3 个知识点: 2题，占比 66.67%
   - 命中 4 个知识点: 0题，占比 0.00%
   - 命中 5 个知识点: 0题，占比 0.00%
   - 命中 6 个知识点: 0题，占比 0.00%
   - 命中 7 个知识点: 0题，占比 0.00%
   - 命中 8 个知识点: 0题，占比 0.00%
   - 命中 9 个知识点: 0题，占比 0.00%
   - 命中 10 个知识点: 0题，占比 0.00%
✅ 结果已保存到: /home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/multi_knowledge_prediction_results_old_test-2.json

🏁 测试完成！
